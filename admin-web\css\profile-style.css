/* Profile Page Styles */
.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 30px;
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.profile-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.avatar-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 48px;
    position: relative;
    overflow: hidden;
}

.avatar-circle img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.avatar-upload-btn {
    font-size: 12px;
    padding: 8px 16px;
}

.profile-info h3 {
    margin: 0 0 10px 0;
    font-size: 28px;
    color: #333;
}

.profile-info p {
    margin: 5px 0;
    color: #666;
    font-size: 16px;
}

.profile-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.profile-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.tab-btn {
    flex: 1;
    padding: 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn:hover {
    background: #e9ecef;
    color: #333;
}

.tab-btn.active {
    background: white;
    color: #007bff;
    border-bottom: 3px solid #007bff;
}

.tab-content {
    padding: 0;
}

.tab-pane {
    display: none;
    padding: 30px;
}

.tab-pane.active {
    display: block;
}

.profile-form {
    margin-bottom: 40px;
}

.profile-form:last-child {
    margin-bottom: 0;
}

.profile-form h4 {
    margin: 0 0 25px 0;
    font-size: 20px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
}

.form-group input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.session-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.session-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.session-details strong {
    display: block;
    margin-bottom: 8px;
    color: #333;
}

.session-details p {
    margin: 4px 0;
    color: #666;
    font-size: 14px;
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.activity-icon.login {
    background-color: #28a745;
}

.activity-icon.logout {
    background-color: #dc3545;
}

.activity-icon.update {
    background-color: #007bff;
}

.activity-icon.create {
    background-color: #17a2b8;
}

.activity-icon.delete {
    background-color: #ffc107;
    color: #333;
}

.activity-details {
    flex: 1;
}

.activity-details strong {
    display: block;
    margin-bottom: 4px;
    color: #333;
}

.activity-details span {
    color: #666;
    font-size: 14px;
}

.activity-time {
    color: #999;
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .profile-tabs {
        flex-direction: column;
    }

    .tab-btn {
        padding: 15px;
        border-bottom: 1px solid #e0e0e0;
    }

    .tab-btn.active {
        border-bottom: 1px solid #e0e0e0;
        border-left: 3px solid #007bff;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .session-item {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .form-actions {
        flex-direction: column;
    }

    .activity-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .profile-container {
        padding: 10px;
    }

    .profile-header,
    .tab-pane {
        padding: 20px;
    }

    .avatar-circle {
        width: 100px;
        height: 100px;
        font-size: 40px;
    }

    .profile-info h3 {
        font-size: 24px;
    }
}
