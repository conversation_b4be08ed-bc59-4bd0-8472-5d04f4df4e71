require('dotenv').config({ path: '../.env' });
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const http = require('http');
const socketIo = require('socket.io');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

const PORT = process.env.KITCHEN_SERVICE_PORT || 3004;

// Import shared modules
const { db, auth } = require('../shared');

// Middleware
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));
app.use(express.json());

// Socket.IO
io.on('connection', (socket) => {
  console.log('Kitchen client connected:', socket.id);

  socket.on('join_kitchen', () => {
    socket.join('kitchen');
    console.log(`Client ${socket.id} joined kitchen room`);
  });

  socket.on('disconnect', () => {
    console.log('Kitchen client disconnected:', socket.id);
  });
});

// Routes

// Lấy danh sách hàng đợi nhà bếp
app.get('/api/queue', auth.authenticateToken, auth.authorizeRole([1, 2, 3]), async (req, res) => {
  try {
    const { status } = req.query;

    let query = `
      SELECT kq.*, od.quantity, od.order_id, f.name as food_name, f.image_url as food_image, t.name as table_name
      FROM kitchen_queue kq
      JOIN order_details od ON kq.order_detail_id = od.id
      JOIN foods f ON od.food_id = f.id
      JOIN orders o ON od.order_id = o.id
      JOIN tables t ON o.table_id = t.id
    `;

    const params = [];

    if (status) {
      query += ' WHERE kq.status = @status';
      params.push({ name: 'status', type: db.sql.NVarChar(30), value: status });
    }

    query += ' ORDER BY kq.updated_at ASC';

    const result = await db.executeQuery(query, params);
    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching kitchen queue:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách hàng đợi nhà bếp' });
  }
});

// Cập nhật trạng thái món ăn trong hàng đợi
app.put('/api/queue/:id/status', auth.authenticateToken, auth.authorizeRole([1, 2, 3]), async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({ message: 'Trạng thái là bắt buộc' });
    }

    // Kiểm tra món ăn tồn tại trong hàng đợi
    const checkResult = await db.executeQuery(`
      SELECT kq.*, od.order_id, t.id as table_id
      FROM kitchen_queue kq
      JOIN order_details od ON kq.order_detail_id = od.id
      JOIN orders o ON od.order_id = o.id
      JOIN tables t ON o.table_id = t.id
      WHERE kq.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: id }]);

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy món ăn trong hàng đợi' });
    }

    const queueItem = checkResult.recordset[0];

    // Cập nhật trạng thái
    const result = await db.executeQuery(`
      UPDATE kitchen_queue
      SET status = @status, updated_at = GETDATE()
      OUTPUT INSERTED.*
      WHERE id = @id
    `, [
      { name: 'status', type: db.sql.NVarChar(30), value: status },
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    // Thông báo qua Socket.IO
    io.to('kitchen').emit('queue_status_updated', {
      queue_id: id,
      status: status,
      order_id: queueItem.order_id,
      table_id: queueItem.table_id
    });

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error updating queue item status:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật trạng thái món ăn trong hàng đợi' });
  }
});

// Nhận thông báo từ Order Service
app.post('/api/notify', async (req, res) => {
  try {
    const { order_id, table_id, added_items } = req.body;

    // Thông báo qua Socket.IO
    io.to('kitchen').emit('new_order_items', {
      order_id,
      table_id,
      added_items
    });

    res.json({ message: 'Đã nhận thông báo' });
  } catch (error) {
    console.error('Error handling notification:', error);
    res.status(500).json({ message: 'Lỗi khi xử lý thông báo' });
  }
});

// Xóa món ăn khỏi hàng đợi (đã giao)
app.delete('/api/queue/:id', auth.authenticateToken, auth.authorizeRole([1, 2, 3]), async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra món ăn tồn tại trong hàng đợi
    const checkResult = await db.executeQuery(`
      SELECT kq.*, od.order_id, t.id as table_id
      FROM kitchen_queue kq
      JOIN order_details od ON kq.order_detail_id = od.id
      JOIN orders o ON od.order_id = o.id
      JOIN tables t ON o.table_id = t.id
      WHERE kq.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: id }]);

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy món ăn trong hàng đợi' });
    }

    const queueItem = checkResult.recordset[0];

    // Xóa khỏi hàng đợi
    await db.executeQuery(`
      DELETE FROM kitchen_queue WHERE id = @id
    `, [{ name: 'id', type: db.sql.Int, value: id }]);

    // Thông báo qua Socket.IO
    io.to('kitchen').emit('queue_item_delivered', {
      queue_id: id,
      order_id: queueItem.order_id,
      table_id: queueItem.table_id
    });

    res.json({ message: 'Đã giao món ăn' });
  } catch (error) {
    console.error('Error delivering queue item:', error);
    res.status(500).json({ message: 'Lỗi khi giao món ăn' });
  }
});

// Xóa tất cả món đã hoàn thành
app.delete('/api/queue/clear-completed', auth.authenticateToken, auth.authorizeRole([1, 2, 3]), async (req, res) => {
  try {
    console.log('Clearing all completed items from kitchen queue...');

    // Lấy danh sách các món đã hoàn thành trước khi xóa
    const completedItemsResult = await db.executeQuery(`
      SELECT kq.*, od.order_id, t.id as table_id
      FROM kitchen_queue kq
      JOIN order_details od ON kq.order_detail_id = od.id
      JOIN orders o ON od.order_id = o.id
      JOIN tables t ON o.table_id = t.id
      WHERE kq.status = 'Hoàn thành'
    `);

    const completedItems = completedItemsResult.recordset;
    console.log(`Found ${completedItems.length} completed items to delete`);

    if (completedItems.length === 0) {
      return res.json({ message: 'Không có món đã hoàn thành để xóa', deletedCount: 0 });
    }

    // Xóa tất cả món đã hoàn thành
    const deleteResult = await db.executeQuery(`
      DELETE FROM kitchen_queue WHERE status = 'Hoàn thành'
    `);

    console.log(`Deleted ${completedItems.length} completed items from kitchen queue`);

    // Thông báo qua Socket.IO
    io.to('kitchen').emit('completed_items_cleared', {
      deletedCount: completedItems.length,
      deletedItems: completedItems.map(item => ({
        queue_id: item.id,
        order_id: item.order_id,
        table_id: item.table_id
      }))
    });

    res.json({
      message: `Đã xóa ${completedItems.length} món đã hoàn thành`,
      deletedCount: completedItems.length
    });
  } catch (error) {
    console.error('Error clearing completed items:', error);
    res.status(500).json({ message: 'Lỗi khi xóa món đã hoàn thành' });
  }
});

// Lấy thống kê nhà bếp
app.get('/api/stats', auth.authenticateToken, auth.authorizeRole([1, 2, 3]), async (req, res) => {
  try {
    const result = await db.executeQuery(`
      SELECT
        (SELECT COUNT(*) FROM kitchen_queue WHERE status = 'Chờ chế biến') as pending_count,
        (SELECT COUNT(*) FROM kitchen_queue WHERE status = 'Đang chế biến') as cooking_count,
        (SELECT COUNT(*) FROM kitchen_queue WHERE status = 'Hoàn thành') as completed_count,
        (SELECT COUNT(*) FROM kitchen_queue) as total_count
    `);

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error fetching kitchen stats:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thống kê nhà bếp' });
  }
});

// Test endpoint để tạo dữ liệu test (chỉ dùng cho development)
app.post('/api/test/create-sample-data', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    console.log('Creating sample kitchen queue data...');

    // Tạo một số dữ liệu test trong kitchen_queue
    const sampleData = [
      { status: 'Chờ chế biến', order_detail_id: 1 },
      { status: 'Đang chế biến', order_detail_id: 2 },
      { status: 'Hoàn thành', order_detail_id: 3 },
      { status: 'Hoàn thành', order_detail_id: 4 },
      { status: 'Hoàn thành', order_detail_id: 5 }
    ];

    let createdCount = 0;
    for (const item of sampleData) {
      try {
        await db.executeQuery(`
          INSERT INTO kitchen_queue (order_detail_id, status, updated_at)
          VALUES (@orderDetailId, @status, GETDATE())
        `, [
          { name: 'orderDetailId', type: db.sql.Int, value: item.order_detail_id },
          { name: 'status', type: db.sql.NVarChar(30), value: item.status }
        ]);
        createdCount++;
      } catch (insertError) {
        console.log(`Failed to insert item with order_detail_id ${item.order_detail_id}:`, insertError.message);
        // Continue with other items
      }
    }

    res.json({
      message: `Created ${createdCount} sample kitchen queue items`,
      createdCount: createdCount
    });
  } catch (error) {
    console.error('Error creating sample data:', error);
    res.status(500).json({ message: 'Lỗi khi tạo dữ liệu test' });
  }
});

// Xử lý lỗi
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Đã xảy ra lỗi server' });
});

// Khởi động server
server.listen(PORT, () => {
  console.log(`Kitchen Service đang chạy tại http://localhost:${PORT}`);
});
