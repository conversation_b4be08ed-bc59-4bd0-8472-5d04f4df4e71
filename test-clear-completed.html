<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Clear Completed API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn.primary {
            background-color: #007bff;
            color: white;
        }
        .btn.warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Clear Completed API</h1>
        <p>This page tests the kitchen clear completed functionality directly.</p>
        
        <div class="input-group">
            <label for="token-input">Authentication Token:</label>
            <input type="text" id="token-input" placeholder="Enter your auth token here">
        </div>

        <div class="action-buttons">
            <button class="btn primary" id="test-auth-btn">Test Authentication</button>
            <button class="btn primary" id="get-stats-btn">Get Kitchen Stats</button>
            <button class="btn warning" id="clear-completed-btn">Clear Completed Items</button>
        </div>

        <div id="result" class="result" style="display: none;">
            <h3>Result:</h3>
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/api';

        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            const content = document.getElementById('result-content');
            content.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        function getToken() {
            return document.getElementById('token-input').value.trim();
        }

        // Test authentication
        document.getElementById('test-auth-btn').addEventListener('click', async () => {
            const token = getToken();
            if (!token) {
                showResult('Please enter a token first', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/users/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const user = await response.json();
                    showResult(`Authentication successful!\nUser: ${JSON.stringify(user, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Authentication failed!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Get kitchen stats
        document.getElementById('get-stats-btn').addEventListener('click', async () => {
            const token = getToken();
            if (!token) {
                showResult('Please enter a token first', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/kitchen/stats`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const stats = await response.json();
                    showResult(`Kitchen Stats:\n${JSON.stringify(stats, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Failed to get stats!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Clear completed items
        document.getElementById('clear-completed-btn').addEventListener('click', async () => {
            const token = getToken();
            if (!token) {
                showResult('Please enter a token first', 'error');
                return;
            }

            const confirmed = confirm('Are you sure you want to clear all completed items?');
            if (!confirmed) {
                return;
            }

            try {
                const response = await fetch(`${API_URL}/kitchen/queue/clear-completed`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (response.ok) {
                    const result = await response.json();
                    showResult(`Clear completed successful!\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Failed to clear completed!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Auto-fill token from localStorage if available
        const savedToken = localStorage.getItem('token');
        if (savedToken) {
            document.getElementById('token-input').value = savedToken;
        }
    </script>
</body>
</html>
