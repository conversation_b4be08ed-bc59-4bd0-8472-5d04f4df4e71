<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Kitchen Buttons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn.secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn.warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Kitchen Buttons</h1>
        <p>This page tests the kitchen refresh and clear completed functionality.</p>
        
        <div class="action-buttons">
            <button class="btn secondary" id="refresh-kitchen-btn">
                <i class="fas fa-sync-alt"></i> Làm mới
            </button>
            <button class="btn warning" id="clear-completed-btn">
                <i class="fas fa-trash"></i> Xóa món đã hoàn thành
            </button>
        </div>

        <div id="result" class="result" style="display: none;">
            <h3>Result:</h3>
            <div id="result-content"></div>
        </div>

        <div id="kitchen-stats">
            <h3>Kitchen Stats:</h3>
            <div>Chờ chế biến: <span id="waiting-count">0</span></div>
            <div>Đang chế biến: <span id="cooking-count">0</span></div>
            <div>Hoàn thành: <span id="completed-count">0</span></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/api';
        let token = 'test-token'; // This would normally come from login

        // Mock notification manager
        const notificationManager = {
            success: (message) => showResult(message, 'success'),
            error: (message) => showResult(message, 'error'),
            info: (message) => showResult(message, 'info')
        };

        function showResult(message, type) {
            const result = document.getElementById('result');
            const content = document.getElementById('result-content');
            content.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        // Test function to load kitchen stats
        async function loadKitchenStats() {
            try {
                const response = await fetch(`${API_URL}/kitchen/stats`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const stats = await response.json();
                    document.getElementById('waiting-count').textContent = stats.pending_count;
                    document.getElementById('cooking-count').textContent = stats.cooking_count;
                    document.getElementById('completed-count').textContent = stats.completed_count;
                } else {
                    console.error('Failed to load kitchen stats');
                }
            } catch (error) {
                console.error('Error loading kitchen stats:', error);
            }
        }

        // Test function to clear completed items
        async function clearCompletedItems() {
            try {
                const confirmed = confirm('Bạn có chắc chắn muốn xóa tất cả món đã hoàn thành?');
                
                if (!confirmed) {
                    return;
                }

                const response = await fetch(`${API_URL}/kitchen/queue/clear-completed`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    notificationManager.success(`Đã xóa ${result.deletedCount || 0} món đã hoàn thành`);
                    await loadKitchenStats();
                } else {
                    const errorData = await response.json();
                    notificationManager.error(errorData.message || 'Không thể xóa món đã hoàn thành');
                }
            } catch (error) {
                console.error('Error clearing completed items:', error);
                notificationManager.error('Lỗi khi xóa món đã hoàn thành: ' + error.message);
            }
        }

        // Setup event handlers
        document.getElementById('refresh-kitchen-btn').addEventListener('click', async () => {
            console.log('Refreshing kitchen data...');
            await loadKitchenStats();
            notificationManager.success('Đã làm mới dữ liệu nhà bếp');
        });

        document.getElementById('clear-completed-btn').addEventListener('click', clearCompletedItems);

        // Load initial data
        loadKitchenStats();
    </script>
</body>
</html>
