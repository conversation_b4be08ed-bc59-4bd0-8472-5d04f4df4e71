<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login and Get Token</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            padding: 10px 20px;
            margin: 10px 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn.primary {
            background-color: #007bff;
            color: white;
        }
        .btn.success {
            background-color: #28a745;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .token-display {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Login and Get Token</h1>
        <p>Use this page to login and get a valid authentication token for testing.</p>

        <form id="login-form">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            <button type="submit" class="btn primary">Login</button>
        </form>

        <div id="token-section" style="display: none;">
            <h3>Authentication Token:</h3>
            <div class="token-display" id="token-display"></div>
            <button class="btn success" id="copy-token-btn">Copy Token</button>
            <button class="btn primary" id="create-sample-btn">Create Sample Data</button>
            <button class="btn primary" id="get-stats-btn">Get Kitchen Stats</button>
            <button class="btn primary" id="test-clear-btn">Test Clear Completed</button>
        </div>

        <div id="result" class="result" style="display: none;">
            <h3>Result:</h3>
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/api';
        let currentToken = null;

        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            const content = document.getElementById('result-content');
            content.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        function showToken(token) {
            currentToken = token;
            document.getElementById('token-display').textContent = token;
            document.getElementById('token-section').style.display = 'block';

            // Save to localStorage for other test pages
            localStorage.setItem('token', token);
        }

        // Handle login
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                showResult('Logging in...', 'info');

                const response = await fetch(`${API_URL}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult(`Login successful!\nUser: ${data.user.username}\nRole: ${data.user.role_name}`, 'success');
                    showToken(data.token);
                } else {
                    const errorText = await response.text();
                    showResult(`Login failed!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Copy token to clipboard
        document.getElementById('copy-token-btn').addEventListener('click', () => {
            if (currentToken) {
                navigator.clipboard.writeText(currentToken).then(() => {
                    showResult('Token copied to clipboard!', 'success');
                }).catch(() => {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = currentToken;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showResult('Token copied to clipboard!', 'success');
                });
            }
        });

        // Create sample data
        document.getElementById('create-sample-btn').addEventListener('click', async () => {
            if (!currentToken) {
                showResult('No token available. Please login first.', 'error');
                return;
            }

            try {
                showResult('Creating sample data...', 'info');

                const response = await fetch(`${API_URL}/kitchen/test/create-sample-data`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`Sample data created!\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Failed to create sample data!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Get kitchen stats
        document.getElementById('get-stats-btn').addEventListener('click', async () => {
            if (!currentToken) {
                showResult('No token available. Please login first.', 'error');
                return;
            }

            try {
                showResult('Getting kitchen stats...', 'info');

                const response = await fetch(`${API_URL}/kitchen/stats`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`Kitchen stats:\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Failed to get stats!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Test clear completed with current token
        document.getElementById('test-clear-btn').addEventListener('click', async () => {
            if (!currentToken) {
                showResult('No token available. Please login first.', 'error');
                return;
            }

            try {
                showResult('Testing clear completed...', 'info');

                const response = await fetch(`${API_URL}/kitchen/queue/clear-completed`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`Clear completed successful!\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Clear completed failed!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Check if there's already a token in localStorage
        const savedToken = localStorage.getItem('token');
        if (savedToken) {
            showToken(savedToken);
            showResult('Found saved token from localStorage', 'info');
        }
    </script>
</body>
</html>
