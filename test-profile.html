<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn.primary {
            background-color: #007bff;
            color: white;
        }
        .btn.success {
            background-color: #28a745;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Profile APIs</h1>
        <p>Test the profile functionality and APIs.</p>
        
        <div class="form-group">
            <label for="token-input">Authentication Token:</label>
            <input type="text" id="token-input" placeholder="Enter your auth token here">
        </div>

        <div class="action-buttons">
            <button class="btn primary" id="login-btn">Login as Admin</button>
            <button class="btn primary" id="get-profile-btn">Get Profile</button>
            <button class="btn success" id="update-profile-btn">Update Profile</button>
            <button class="btn success" id="change-password-btn">Change Password</button>
        </div>

        <div id="result" class="result" style="display: none;">
            <h3>Result:</h3>
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/api';

        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            const content = document.getElementById('result-content');
            content.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        function getToken() {
            return document.getElementById('token-input').value.trim();
        }

        // Auto-fill token from localStorage if available
        const savedToken = localStorage.getItem('token');
        if (savedToken) {
            document.getElementById('token-input').value = savedToken;
        }

        // Login as admin
        document.getElementById('login-btn').addEventListener('click', async () => {
            try {
                showResult('Logging in as admin...', 'info');
                
                const response = await fetch(`${API_URL}/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        username: 'admin', 
                        password: 'admin123' 
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('token-input').value = data.token;
                    localStorage.setItem('token', data.token);
                    showResult(`Login successful!\nUser: ${data.user.username}\nRole: ${data.user.role_name}\nToken: ${data.token}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Login failed!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Get profile
        document.getElementById('get-profile-btn').addEventListener('click', async () => {
            const token = getToken();
            if (!token) {
                showResult('Please enter a token first', 'error');
                return;
            }

            try {
                showResult('Getting profile...', 'info');
                
                const response = await fetch(`${API_URL}/users/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const profile = await response.json();
                    showResult(`Profile data:\n${JSON.stringify(profile, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Failed to get profile!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Update profile
        document.getElementById('update-profile-btn').addEventListener('click', async () => {
            const token = getToken();
            if (!token) {
                showResult('Please enter a token first', 'error');
                return;
            }

            try {
                showResult('Updating profile...', 'info');
                
                const updateData = {
                    full_name: 'Admin Updated',
                    email: '<EMAIL>',
                    phone_number: '0123456789',
                    age: 30,
                    address: '123 Test Street, Test City'
                };

                const response = await fetch(`${API_URL}/users/me`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`Profile updated successfully!\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Failed to update profile!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });

        // Change password
        document.getElementById('change-password-btn').addEventListener('click', async () => {
            const token = getToken();
            if (!token) {
                showResult('Please enter a token first', 'error');
                return;
            }

            try {
                showResult('Changing password...', 'info');
                
                const passwordData = {
                    currentPassword: 'admin123',
                    newPassword: 'newpassword123'
                };

                const response = await fetch(`${API_URL}/users/change-password`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(passwordData)
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`Password changed successfully!\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`Failed to change password!\nStatus: ${response.status}\nError: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`Network error: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
