require('dotenv').config({ path: '../.env' });
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const bcrypt = require('bcrypt');

const app = express();
const PORT = process.env.USER_SERVICE_PORT || 3005;

// Import shared modules
const { db, auth } = require('../shared');

// Middleware
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));
app.use(express.json());

// Routes

// Đăng nhập
app.post('/api/login', async (req, res) => {
  console.log('Login request received:', req.body);
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ message: 'Tê<PERSON> đăng nhập và mật khẩu là bắt buộc' });
    }

    // Tìm người dùng
    const result = await db.executeQuery(
      'SELECT u.*, r.name as role_name FROM users u JOIN roles r ON u.role_id = r.id WHERE u.username = @username',
      [{ name: 'username', type: db.sql.NVarChar(50), value: username }]
    );

    if (result.recordset.length === 0) {
      return res.status(401).json({ message: 'Tên đăng nhập hoặc mật khẩu không đúng' });
    }

    const user = result.recordset[0];

    // Kiểm tra xem mật khẩu đã được mã hóa hay chưa
    let isPasswordValid = false;

    if (user.password.startsWith('$2')) {
      // Mật khẩu đã được mã hóa bằng bcrypt
      try {
        isPasswordValid = await bcrypt.compare(password, user.password);
      } catch (error) {
        console.error('Lỗi khi so sánh mật khẩu:', error);
        isPasswordValid = false;
      }
    } else {
      // Mật khẩu chưa được mã hóa, so sánh trực tiếp
      isPasswordValid = (password === user.password);

      // Nếu đúng mật khẩu, cập nhật mật khẩu thành dạng mã hóa
      if (isPasswordValid) {
        try {
          const hashedPassword = await bcrypt.hash(password, 10);
          await db.executeQuery(
            'UPDATE users SET password = @password, updated_at = GETDATE() WHERE id = @id',
            [
              { name: 'password', type: db.sql.NVarChar(100), value: hashedPassword },
              { name: 'id', type: db.sql.Int, value: user.id }
            ]
          );
          console.log(`Đã cập nhật mật khẩu mã hóa cho người dùng ${user.username}`);
        } catch (error) {
          console.error('Lỗi khi cập nhật mật khẩu mã hóa:', error);
        }
      }
    }

    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Tên đăng nhập hoặc mật khẩu không đúng' });
    }

    // Tạo token
    const token = auth.generateToken(user);

    // Ghi log đăng nhập
    await db.executeQuery(
      'INSERT INTO logs (user_id, action, created_at) VALUES (@userId, @action, GETDATE())',
      [
        { name: 'userId', type: db.sql.Int, value: user.id },
        { name: 'action', type: db.sql.NVarChar(255), value: 'Đăng nhập' }
      ]
    );

    // Trả về thông tin người dùng và token
    const { password: _, ...userWithoutPassword } = user;
    res.json({
      user: userWithoutPassword,
      token
    });
  } catch (error) {
    console.error('Error during login:', error);
    res.status(500).json({ message: 'Lỗi khi đăng nhập' });
  }
});

// Lấy thông tin người dùng hiện tại
app.get('/api/me', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.user;

    const result = await db.executeQuery(
      'SELECT u.*, r.name as role_name FROM users u JOIN roles r ON u.role_id = r.id WHERE u.id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy người dùng' });
    }

    const { password, ...userWithoutPassword } = result.recordset[0];
    res.json(userWithoutPassword);
  } catch (error) {
    console.error('Error fetching current user:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin người dùng' });
  }
});

// Cập nhật thông tin profile của người dùng hiện tại
app.put('/api/me', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.user;
    const { full_name, phone_number, age, email, address, avatar } = req.body;

    // Kiểm tra người dùng tồn tại
    const checkResult = await db.executeQuery(
      'SELECT * FROM users WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy người dùng' });
    }

    // Cập nhật thông tin
    let updateQuery = 'UPDATE users SET updated_at = GETDATE()';
    const params = [];

    if (full_name !== undefined) {
      updateQuery += ', full_name = @fullName';
      params.push({ name: 'fullName', type: db.sql.NVarChar(100), value: full_name });
    }

    if (phone_number !== undefined) {
      updateQuery += ', phone_number = @phoneNumber';
      params.push({ name: 'phoneNumber', type: db.sql.NVarChar(20), value: phone_number });
    }

    if (age !== undefined) {
      updateQuery += ', age = @age';
      params.push({ name: 'age', type: db.sql.Int, value: age });
    }

    if (email !== undefined) {
      updateQuery += ', email = @email';
      params.push({ name: 'email', type: db.sql.NVarChar(100), value: email });
    }

    if (address !== undefined) {
      updateQuery += ', address = @address';
      params.push({ name: 'address', type: db.sql.NVarChar(255), value: address });
    }

    if (avatar !== undefined) {
      updateQuery += ', avatar = @avatar';
      params.push({ name: 'avatar', type: db.sql.NVarChar(255), value: avatar });
    }

    updateQuery += ' OUTPUT INSERTED.id, INSERTED.username, INSERTED.role_id, INSERTED.full_name, INSERTED.phone_number, INSERTED.age, INSERTED.email, INSERTED.address, INSERTED.avatar, INSERTED.created_at, INSERTED.updated_at WHERE id = @id';
    params.push({ name: 'id', type: db.sql.Int, value: id });

    const result = await db.executeQuery(updateQuery, params);

    // Lấy thông tin vai trò
    const roleResult = await db.executeQuery(
      'SELECT name FROM roles WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: result.recordset[0].role_id }]
    );

    const user = result.recordset[0];
    user.role_name = roleResult.recordset[0].name;

    res.json(user);
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật thông tin cá nhân' });
  }
});

// Đổi mật khẩu
app.put('/api/change-password', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.user;
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ message: 'Mật khẩu hiện tại và mật khẩu mới là bắt buộc' });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ message: 'Mật khẩu mới phải có ít nhất 6 ký tự' });
    }

    // Lấy thông tin người dùng
    const userResult = await db.executeQuery(
      'SELECT * FROM users WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (userResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy người dùng' });
    }

    const user = userResult.recordset[0];

    // Kiểm tra mật khẩu hiện tại
    let isCurrentPasswordValid = false;

    if (user.password.startsWith('$2')) {
      // Mật khẩu đã được mã hóa bằng bcrypt
      isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    } else {
      // Mật khẩu chưa được mã hóa
      isCurrentPasswordValid = (currentPassword === user.password);
    }

    if (!isCurrentPasswordValid) {
      return res.status(400).json({ message: 'Mật khẩu hiện tại không đúng' });
    }

    // Mã hóa mật khẩu mới
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Cập nhật mật khẩu
    await db.executeQuery(
      'UPDATE users SET password = @password, updated_at = GETDATE() WHERE id = @id',
      [
        { name: 'password', type: db.sql.NVarChar(100), value: hashedNewPassword },
        { name: 'id', type: db.sql.Int, value: id }
      ]
    );

    // Ghi log
    await db.executeQuery(
      'INSERT INTO logs (user_id, action, created_at) VALUES (@userId, @action, GETDATE())',
      [
        { name: 'userId', type: db.sql.Int, value: id },
        { name: 'action', type: db.sql.NVarChar(255), value: 'Đổi mật khẩu' }
      ]
    );

    res.json({ message: 'Đổi mật khẩu thành công' });
  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({ message: 'Lỗi khi đổi mật khẩu' });
  }
});

// Lấy danh sách tất cả người dùng
app.get('/api/users', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const result = await db.executeQuery(`
      SELECT u.id, u.username, u.role_id, r.name as role_name, u.full_name,
             u.phone_number, u.age, u.email, u.address, u.created_at, u.updated_at
      FROM users u
      JOIN roles r ON u.role_id = r.id
      ORDER BY u.id
    `);

    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách người dùng' });
  }
});

// Lấy thông tin một người dùng cụ thể
app.get('/api/users/:id', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.executeQuery(`
      SELECT u.id, u.username, u.role_id, r.name as role_name, u.full_name,
             u.phone_number, u.age, u.email, u.address, u.created_at, u.updated_at
      FROM users u
      JOIN roles r ON u.role_id = r.id
      WHERE u.id = @id
    `, [{ name: 'id', type: db.sql.Int, value: id }]);

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy người dùng' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin người dùng' });
  }
});

// Tạo người dùng mới
app.post('/api/users', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const {
      username, password, role_id, full_name,
      phone_number, age, email, address
    } = req.body;

    if (!username || !password || !role_id) {
      return res.status(400).json({ message: 'Tên đăng nhập, mật khẩu và vai trò là bắt buộc' });
    }

    // Kiểm tra username đã tồn tại chưa
    const checkResult = await db.executeQuery(
      'SELECT COUNT(*) as count FROM users WHERE username = @username',
      [{ name: 'username', type: db.sql.NVarChar(50), value: username }]
    );

    if (checkResult.recordset[0].count > 0) {
      return res.status(400).json({ message: 'Tên đăng nhập đã tồn tại' });
    }

    // Mã hóa mật khẩu
    const hashedPassword = await bcrypt.hash(password, 10);

    // Tạo người dùng mới
    const result = await db.executeQuery(`
      INSERT INTO users (
        username, password, role_id, full_name,
        phone_number, age, email, address,
        created_at, updated_at
      )
      OUTPUT INSERTED.id, INSERTED.username, INSERTED.role_id, INSERTED.full_name,
             INSERTED.phone_number, INSERTED.age, INSERTED.email, INSERTED.address,
             INSERTED.created_at, INSERTED.updated_at
      VALUES (
        @username, @password, @roleId, @fullName,
        @phoneNumber, @age, @email, @address,
        GETDATE(), GETDATE()
      )
    `, [
      { name: 'username', type: db.sql.NVarChar(50), value: username },
      { name: 'password', type: db.sql.NVarChar(100), value: hashedPassword },
      { name: 'roleId', type: db.sql.Int, value: role_id },
      { name: 'fullName', type: db.sql.NVarChar(100), value: full_name || null },
      { name: 'phoneNumber', type: db.sql.NVarChar(20), value: phone_number || null },
      { name: 'age', type: db.sql.Int, value: age || null },
      { name: 'email', type: db.sql.NVarChar(100), value: email || null },
      { name: 'address', type: db.sql.NVarChar(255), value: address || null }
    ]);

    // Lấy thông tin vai trò
    const roleResult = await db.executeQuery(
      'SELECT name FROM roles WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: role_id }]
    );

    const user = result.recordset[0];
    user.role_name = roleResult.recordset[0].name;

    res.status(201).json(user);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ message: 'Lỗi khi tạo người dùng mới' });
  }
});

// Cập nhật thông tin người dùng
app.put('/api/users/:id', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      role_id, full_name, phone_number,
      age, email, address, password
    } = req.body;

    // Kiểm tra quyền
    if (req.user.id != id && req.user.role_id != 1) {
      return res.status(403).json({ message: 'Không có quyền cập nhật thông tin người dùng khác' });
    }

    // Chỉ admin mới có thể cập nhật vai trò
    if (role_id && req.user.role_id != 1) {
      return res.status(403).json({ message: 'Không có quyền cập nhật vai trò' });
    }

    // Kiểm tra người dùng tồn tại
    const checkResult = await db.executeQuery(
      'SELECT * FROM users WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy người dùng' });
    }

    // Cập nhật thông tin
    let updateQuery = 'UPDATE users SET updated_at = GETDATE()';
    const params = [];

    if (role_id) {
      updateQuery += ', role_id = @roleId';
      params.push({ name: 'roleId', type: db.sql.Int, value: role_id });
    }

    if (full_name) {
      updateQuery += ', full_name = @fullName';
      params.push({ name: 'fullName', type: db.sql.NVarChar(100), value: full_name });
    }

    if (phone_number) {
      updateQuery += ', phone_number = @phoneNumber';
      params.push({ name: 'phoneNumber', type: db.sql.NVarChar(20), value: phone_number });
    }

    if (age) {
      updateQuery += ', age = @age';
      params.push({ name: 'age', type: db.sql.Int, value: age });
    }

    if (email) {
      updateQuery += ', email = @email';
      params.push({ name: 'email', type: db.sql.NVarChar(100), value: email });
    }

    if (address) {
      updateQuery += ', address = @address';
      params.push({ name: 'address', type: db.sql.NVarChar(255), value: address });
    }

    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      updateQuery += ', password = @password';
      params.push({ name: 'password', type: db.sql.NVarChar(100), value: hashedPassword });
    }

    updateQuery += ' OUTPUT INSERTED.id, INSERTED.username, INSERTED.role_id, INSERTED.full_name, INSERTED.phone_number, INSERTED.age, INSERTED.email, INSERTED.address, INSERTED.created_at, INSERTED.updated_at WHERE id = @id';
    params.push({ name: 'id', type: db.sql.Int, value: id });

    const result = await db.executeQuery(updateQuery, params);

    // Lấy thông tin vai trò
    const roleResult = await db.executeQuery(
      'SELECT name FROM roles WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: result.recordset[0].role_id }]
    );

    const user = result.recordset[0];
    user.role_name = roleResult.recordset[0].name;

    res.json(user);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật thông tin người dùng' });
  }
});

// Xóa người dùng
app.delete('/api/users/:id', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra người dùng tồn tại
    const checkResult = await db.executeQuery(
      'SELECT * FROM users WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy người dùng' });
    }

    // Kiểm tra người dùng có đơn hàng không
    const orderResult = await db.executeQuery(
      'SELECT COUNT(*) as count FROM orders WHERE user_id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (orderResult.recordset[0].count > 0) {
      return res.status(400).json({ message: 'Không thể xóa người dùng đã có đơn hàng' });
    }

    // Xóa logs của người dùng
    await db.executeQuery(
      'DELETE FROM logs WHERE user_id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    // Xóa người dùng
    const result = await db.executeQuery(
      'DELETE FROM users OUTPUT DELETED.id, DELETED.username, DELETED.role_id WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    res.json({ message: 'Đã xóa người dùng thành công', user: result.recordset[0] });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Lỗi khi xóa người dùng' });
  }
});

// Lấy danh sách vai trò
app.get('/api/roles', async (req, res) => {
  try {
    const result = await db.executeQuery('SELECT * FROM roles');
    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching roles:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách vai trò' });
  }
});

// Xử lý lỗi
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Đã xảy ra lỗi server' });
});

// Khởi động server
app.listen(PORT, () => {
  console.log(`User Service đang chạy tại http://localhost:${PORT}`);
});
